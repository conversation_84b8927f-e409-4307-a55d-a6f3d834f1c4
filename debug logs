PS C:\Users\<USER>\Desktop\StreamVista> npm run dev

> stream-vista@0.1.0 dev
> next dev -H 0.0.0.0

   ▲ Next.js 15.2.3
   - Local:        http://localhost:3000
   - Network:      http://0.0.0.0:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 4s
 ✓ Compiled /middleware in 314ms (101 modules)
 ○ Compiling /watch/[id] ...
 ✓ Compiled /watch/[id] in 6.9s (2636 modules)
Auth state: { authState: false, userId: undefined }
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 8877ms
 ○ Compiling /api/tracking/visitor ...
 ✓ Compiled /api/notifications in 10s (2734 modules)
Starting session validation process
Starting session validation process
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752425486821; HstPn4873540=27; HstPt4873540=1094',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/watch/1233413?forcePlay=true&contentType=movie',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Finding user by ID for session validation: 67f2bb470ba03bb2e0912c3f
 POST /api/watch-party 200 in 11734ms
User model imported successfully
Cookies in request: [
  'HstCfa4873540',
  'visitorId',
  'HstCmu4873540',
  '_cc_id',
  'panoramaId',
  'panoramaIdType',
  '__next_hmr_refresh_hash__',
  'HstCfa4896283',
  'HstCmu4896283',
  'HstCnv4896283',
  'HstCns4896283',
  'HstCla4896283',
  'HstPn4896283',
  'HstPt4896283',
  'HstCnv4873540',
  'HstCns4873540',
  'panoramaId_expiry',
  'HstCla4873540',
  'HstPn4873540',
  'HstPt4873540'
]
userId from cookie: undefined
Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.9',
  connection: 'keep-alive',
  'content-length': '37',
  'content-type': 'application/json',
  cookie: 'HstCfa4873540=1742795967999; visitorId=56be20fa-25d2-46f7-8134-56ac6bb373d7; HstCmu4873540=1751584332381; _cc_id=fca61f2f8e22f9d744b5c65192db15f6; panoramaId=8f0f9f8d5f8e63f841f09fba9b1f16d539386a12b13a2ced6b1eb70c133ae381; panoramaIdType=panoIndiv; __next_hmr_refresh_hash__=58ec8754161246976eaa7bdf360ab5b66d2c86b48cc87fd2; HstCfa4896283=1752183571480; HstCmu4896283=1752183571480; HstCnv4896283=1; HstCns4896283=1; HstCla4896283=1752183689558; HstPn4896283=4; HstPt4896283=4; HstCnv4873540=59; HstCns4873540=99; panoramaId_expiry=1753028861028; HstCla4873540=1752425486821; HstPn4873540=27; HstPt4873540=1094',
  host: 'localhost:3000',
  origin: 'http://localhost:3000',
  referer: 'http://localhost:3000/watch/1233413?forcePlay=true&contentType=movie',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '127.0.0.1',
  'x-forwarded-host': 'localhost:3000',
  'x-forwarded-port': '3000',
  'x-forwarded-proto': 'http'
}
Session data parsed successfully
userId from request body: 67f2bb470ba03bb2e0912c3f
Finding user by ID for session validation: 67f2bb470ba03bb2e0912c3f
User found successfully
Session validation successful for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 12709ms
 POST /api/watch-party 200 in 33ms
 GET /api/profiles?userId=67f2bb470ba03bb2e0912c3f 200 in 12803ms
User found successfully
Session validation successful for user ID: 67f2bb470ba03bb2e0912c3f
 POST /api/auth/session 200 in 13048ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 1335ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 13141ms
 ○ Compiling /api/content ...
 GET /api/notifications?userId=67f2bb470ba03bb2e0912c3f 200 in 12747ms
 ✓ Compiled /api/profiles/[profileId]/my-list in 1929ms (2730 modules)
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 2010ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
 POST /api/watch-party 200 in 86ms
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 2736ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 279ms
 ○ Compiling /api/related-content ...
 ✓ Compiled /api/related-content in 1974ms (2733 modules)
API: Fetching related content for movie with ID 1233413
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 2324ms
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
 GET /api/profiles/67f2bb470ba03bb2e0912c41/my-list 200 in 4734ms
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 2129ms
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 146ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
API: Fetching related content for movie with ID 1233413
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 108ms
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 233ms
 ○ Compiling /api/proxy/vidsrc ...
 ✓ Compiled /api/proxy/vidsrc in 1015ms (2735 modules)
Proxying request to: https://vidsrc.xyz/embed/movie?imdb=tt31193180
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 1094ms
 GET /api/proxy/vidsrc?url=https%3A%2F%2Fvidsrc.xyz%2Fembed%2Fmovie%3Fimdb%3Dtt31193180 200 in 2058ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 733ms (2736 modules)
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /style.css?t=1710289820 404 in 1383ms
 GET /base64.js?t=1688387834 404 in 1325ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /sources.js?t=1745104089 404 in 159ms
 GET /reporting.js?t=1688387834 404 in 157ms
 GET /sbx.js?t=1688387834 404 in 148ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752434607 404 in 55ms
