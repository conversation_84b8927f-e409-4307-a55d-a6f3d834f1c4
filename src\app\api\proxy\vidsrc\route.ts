import { NextRequest, NextResponse } from 'next/server';

// Valid domains that can be proxied
const VALID_DOMAINS = ['vidsrc.xyz', 'vidsrc.in', 'vidsrc.pm', 'vidsrc.net'];

// Cache duration in seconds
const CACHE_DURATION = 3600; // 1 hour

// Header modifications to help avoid x-frame issues
const MODIFIED_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'X-Frame-Options': 'ALLOWALL',
  'Content-Security-Policy': "frame-ancestors 'self' *; media-src * blob:; connect-src * blob:; object-src * blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' * blob:; frame-src * blob:; worker-src * blob:; img-src * blob: data:;",
  'Cache-Control': `public, max-age=${CACHE_DURATION}`,
  'Feature-Policy': "autoplay *; fullscreen *; picture-in-picture *; encrypted-media *;",
  'Permissions-Policy': "autoplay=*, fullscreen=*, picture-in-picture=*, encrypted-media=*",
};

/**
 * Proxy API route for VidSrc embed requests
 *
 * This proxy helps bypass x-frame restrictions by making the request from our own server
 * and removing any problematic headers from the response.
 *
 * @param request The incoming request
 * @returns The proxied response
 */
export async function GET(request: NextRequest) {
  try {
    // Get target URL from query params
    const { searchParams } = new URL(request.url);
    const targetUrl = searchParams.get('url');

    // Check if this is a resource request (CSS, JS, etc.) from VidSrc
    const requestUrl = new URL(request.url);
    const pathname = requestUrl.pathname;

    // If no URL parameter is provided, check if this is a resource request
    if (!targetUrl) {
      // Check if this looks like a VidSrc resource request
      const resourceExtensions = ['.css', '.js', '.json', '.woff', '.woff2', '.ttf', '.eot', '.svg', '.png', '.jpg', '.jpeg', '.gif'];
      const hasResourceExtension = resourceExtensions.some(ext => pathname.includes(ext));
      const hasTimestamp = requestUrl.search.includes('t=') || requestUrl.search.includes('_=');

      if (hasResourceExtension || hasTimestamp) {
        // This looks like a resource request, try to proxy it from VidSrc
        const resourcePath = pathname.replace('/api/proxy/vidsrc', '');
        const vidsrcResourceUrl = `https://vidsrc.xyz${resourcePath}${requestUrl.search}`;

        console.log(`Proxying VidSrc resource: ${vidsrcResourceUrl}`);

        try {
          const response = await fetch(vidsrcResourceUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
              'Accept': '*/*',
              'Referer': 'https://vidsrc.xyz/',
              'Origin': 'https://vidsrc.xyz',
            },
            cache: 'no-store',
          });

          if (response.ok) {
            const content = await response.arrayBuffer();
            const contentType = response.headers.get('Content-Type') || 'application/octet-stream';

            return new NextResponse(content, {
              status: 200,
              headers: {
                'Content-Type': contentType,
                'Cache-Control': 'public, max-age=3600',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              }
            });
          }
        } catch (error) {
          console.log(`Failed to fetch VidSrc resource: ${vidsrcResourceUrl}`, error);
        }
      }

      return NextResponse.json(
        { error: 'Missing required parameter: url' },
        { status: 400 }
      );
    }

    // Extract domain from URL and validate it
    const urlObj = new URL(targetUrl);
    const domain = urlObj.hostname;

    if (!VALID_DOMAINS.includes(domain)) {
      return NextResponse.json(
        { error: 'Invalid domain. Only VidSrc domains are allowed.' },
        { status: 400 }
      );
    }

    console.log(`Proxying request to: ${targetUrl}`);

    // First check if domain is reachable with a quick HEAD request
    try {
      const domainCheckController = new AbortController();
      const domainCheckTimeout = setTimeout(() => domainCheckController.abort(), 2000);
      await fetch(`https://${domain}`, {
        method: 'HEAD',
        signal: domainCheckController.signal
      });
      clearTimeout(domainCheckTimeout);
    } catch (err) {
      console.log(`Domain ${domain} appears unreachable, providing fallback content`);
      return new NextResponse(
        createFallbackContent(targetUrl, `Unable to connect to ${domain}`),
        {
          status: 200,
          headers: {
            'Content-Type': 'text/html',
            ...MODIFIED_HEADERS
          }
        }
      );
    }

    // Make the request to the target URL with custom headers
    // Use a longer timeout to ensure we get a response
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8s timeout for faster response

    const response = await fetch(targetUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': `https://${domain}`,
        'Origin': `https://${domain}`,
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
      },
      signal: controller.signal,
      cache: 'no-store', // Ensure fresh content
    }).finally(() => clearTimeout(timeoutId));

    let content;
    let contentType;

    if (!response.ok) {
      // For 404 errors, preserve the status to help with debugging
      if (response.status === 404) {
        console.log(`Content not found (404) from ${domain} for URL: ${targetUrl}`);
        return NextResponse.json(
          { error: 'Content not found on VidSrc', status: 404, url: targetUrl },
          { status: 404 }
        );
      }

      // For other errors (403, 500, etc.), try to create a fallback
      console.log(`Received ${response.status} from ${domain}, creating enhanced fallback`);
      contentType = 'text/html';
      content = createFallbackContent(targetUrl, `Server returned ${response.status} ${response.statusText}`);
    } else {
      // Get the response content
      content = await response.text();
      contentType = response.headers.get('Content-Type') || 'text/html';

      // If HTML content, modify it to improve embedding
      if (contentType.includes('text/html')) {
        // Enhanced content modifications to ensure proper embedding
        content = enhanceEmbedContent(content, domain);
      }
    }

    // Create a new response with modified headers
    const proxyResponse = new NextResponse(content, {
      status: response.ok ? 200 : (response.status === 404 ? 404 : 200), // Preserve 404 status, fallback others to 200
      headers: {
        'Content-Type': contentType,
        ...MODIFIED_HEADERS
      }
    });

    return proxyResponse;
  } catch (error) {
    console.error('Proxy error:', error);

    // Provide a fallback HTML response
    const fallbackHtml = createFallbackContent('', `Proxy error: ${error instanceof Error ? error.message : 'Unknown error'}`);

    return new NextResponse(fallbackHtml, {
      status: 500,
      headers: {
        'Content-Type': 'text/html',
        ...MODIFIED_HEADERS
      }
    });
  }
}

/**
 * Creates enhanced fallback content that tries multiple approaches to load the video
 */
function createFallbackContent(targetUrl: string, errorMessage: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
      <title>VidSrc Player</title>
      <style>
        body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; background: #000; }
        iframe { width: 100%; height: 100%; border: none; }
        .message { color: white; text-align: center; padding: 20px; font-family: Arial, sans-serif; }
        .error { color: #f44336; margin-top: 10px; font-size: 14px; }
        .retry-btn { background: #1e40af; color: white; border: none; padding: 8px 16px;
                     border-radius: 4px; cursor: pointer; margin-top: 15px; }
        /* iOS-specific styles */
        video::-webkit-media-controls-start-playback-button {
          display: block !important;
          opacity: 1 !important;
          pointer-events: auto !important;
        }
      </style>
    </head>
    <body>
      <div id="player-container" style="width: 100%; height: 100%;"></div>

      <script>
        // Multiple fallback mechanisms
        const playerContainer = document.getElementById('player-container');
        const targetUrl = ${JSON.stringify(targetUrl)};
        const errorMessage = ${JSON.stringify(errorMessage)};
        let attemptCount = 0;
        const maxAttempts = 3;

        // Detect iOS devices
        const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent) ||
                     (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform));

        console.log('Device detection - iOS:', isIOS);

        // First try: iframe with normal embed
        function tryDirectEmbed() {
          attemptCount++;
          console.log('Attempt ' + attemptCount + ': direct iframe embed');

          // Use different attributes for iOS devices
          if (isIOS) {
            playerContainer.innerHTML = '<iframe src="' + targetUrl + '" allowfullscreen="true" ' +
              'allow="autoplay; encrypted-media; picture-in-picture; fullscreen; web-share" ' +
              'playsinline webkit-playsinline="true" x-webkit-airplay="allow"></iframe>';
          } else {
            playerContainer.innerHTML = '<iframe src="' + targetUrl + '" allowfullscreen ' +
              'allow="autoplay; encrypted-media; picture-in-picture; fullscreen"></iframe>';
          }

          // Set fallback message if iframe fails to load
          const iframe = playerContainer.querySelector('iframe');
          iframe.onerror = tryFallbackEmbed;

          // Also set a timeout fallback for when iframe loads but content doesn't work
          setTimeout(function() {
            try {
              const iframeContent = iframe.contentDocument || iframe.contentWindow.document;
              if (!iframeContent || !iframeContent.body ||
                  iframeContent.body.innerHTML.includes('refused to connect') ||
                  iframeContent.body.innerHTML.includes('error')) {
                tryFallbackEmbed();
              }
            } catch(e) {
              // Cross-origin restriction may prevent checking - wait a bit longer then try next method
              setTimeout(tryFallbackEmbed, isIOS ? 8000 : 5000); // Longer timeout for iOS
            }
          }, isIOS ? 8000 : 5000); // Longer timeout for iOS
        }

        // Second try: iframe with adjusted attributes
        function tryFallbackEmbed() {
          if (attemptCount >= maxAttempts) {
            showErrorMessage();
            return;
          }

          attemptCount++;
          console.log('Attempt ' + attemptCount + ': fallback with adjusted attributes');

          // Extract the domain from the target URL
          const domain = new URL(targetUrl).hostname;

          // Use different sandbox attributes for iOS
          if (isIOS) {
            playerContainer.innerHTML = '<iframe src="' + targetUrl + '" allowfullscreen="true" ' +
              'sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-orientation-lock allow-downloads allow-modals" ' +
              'referrerpolicy="no-referrer" loading="lazy" ' +
              'playsinline webkit-playsinline="true" x-webkit-airplay="allow"></iframe>';
          } else {
            playerContainer.innerHTML = '<iframe src="' + targetUrl + '" allowfullscreen="true" ' +
              'sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation" ' +
              'referrerpolicy="no-referrer" loading="lazy"></iframe>';
          }

          // If this still fails, try object/embed approach after a short delay
          setTimeout(function() {
            const iframe = playerContainer.querySelector('iframe');
            try {
              const iframeContent = iframe.contentDocument || iframe.contentWindow.document;
              if (!iframeContent || !iframeContent.body || iframeContent.body.innerHTML.includes('error')) {
                showErrorMessage();
              }
            } catch(e) {
              // Wait a bit longer before showing error
              setTimeout(showErrorMessage, isIOS ? 5000 : 3000); // Longer timeout for iOS
            }
          }, isIOS ? 8000 : 5000); // Longer timeout for iOS
        }

        // Final fallback: show error message with retry button
        function showErrorMessage() {
          playerContainer.innerHTML = '<div class="message">' +
            '<div>Unable to load video player</div>' +
            '<div class="error">' + errorMessage + '</div>' +
            '<button class="retry-btn" onclick="window.location.reload()">Retry</button>' +
            '</div>';
        }

        // Start the attempt cascade
        tryDirectEmbed();
      </script>
    </body>
    </html>
  `;
}

/**
 * Enhances HTML content for better embedding
 */
function enhanceEmbedContent(content: string, domain: string): string {
  return content
    // Add base href to resolve relative URLs correctly and viewport meta for mobile
    .replace(/<head>/i, `<head><base href="https://${domain}/" target="_blank"><meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">`)

    // Add necessary CSP headers via meta tag
    .replace(/<head>/i, '<head><meta http-equiv="Content-Security-Policy" content="frame-ancestors *">')

    // Add viewport meta for proper responsive sizing
    .replace(/<head>/i, '<head><meta name="viewport" content="width=device-width, initial-scale=1.0">')

    // Add proper styles to ensure full size display with no scrollbars
    .replace(/<head>/i, `<head>
      <style>
        html, body {
          margin: 0 !important;
          padding: 0 !important;
          height: 100vh !important;
          width: 100vw !important;
          overflow: hidden !important;
        }

        /* Aggressive scrollbar hiding */
        ::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
          height: 0 !important;
          background: transparent !important;
        }

        * {
          -ms-overflow-style: none !important;  /* IE and Edge */
          scrollbar-width: none !important;     /* Firefox */
          scrollbar-color: transparent transparent !important;
        }

        /* Target video containers - preserve aspect ratio */
        iframe, video, .plyr, .jw-video, video-js, .player-container,
        div[id^="player"], div[class*="player"],
        div[class*="container"], div[class*="wrapper"] {
          width: 100% !important;
          height: 100% !important;
          max-width: 100% !important;
          max-height: 100% !important;
          min-height: 100% !important;
          position: absolute !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          overflow: hidden !important;
          margin: 0 !important;
          padding: 0 !important;
          object-fit: contain !important;
        }

        /* Fix for specific hitstv.tv player elements */
        .hitstv-wrapper, .hitstv-container, .hitstv-player,
        .hitstv_wrapper, .hitstv_container, .hitstv_player,
        #hitstv-container, #hitstv-player,
        div[id*="hitstv"], div[class*="hitstv"] {
          position: relative !important;
          overflow: hidden !important;
          width: 100% !important;
          height: 100% !important;
          max-width: 100% !important;
          max-height: 100% !important;
          aspect-ratio: 16/9 !important;
          margin: 0 !important;
          padding: 0 !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        /* Special fix for hitstv video elements */
        [id*="hitstv"] video, [class*="hitstv"] video,
        [id*="hitstv"] .video-js, [class*="hitstv"] .video-js,
        [id*="hitstv"] .bitmovin-player, [class*="hitstv"] .bitmovin-player,
        [id*="hitstv"] .player-container, [class*="hitstv"] .player-container {
          position: relative !important;
          height: auto !important;
          min-height: auto !important;
          width: 100% !important;
          aspect-ratio: 16/9 !important;
          object-fit: contain !important;
        }

        /* Fix for vidsrc.me embedded player */
        .vidsrc-player, #vidsrc-player,
        div[id*="vidsrc"], div[class*="vidsrc"],
        #player, .player, [id*="player-container"] {
          overflow: hidden !important;
          width: 100% !important;
          height: 100% !important;
          position: absolute !important;
        }

        /* Common player layouts */
        .jw-aspect, .plyr__video-wrapper, .video-js, video-js,
        .video-container, .player-frame, .embed-responsive,
        .media-player, .video-player {
          position: absolute !important;
          width: 100% !important;
          height: 100% !important;
          top: 0 !important;
          left: 0 !important;
          overflow: hidden !important;
        }

        /* Fix for common overflow issues */
        div, section, main, article, nav, header, footer {
          overflow: visible !important;
        }

        /* Only apply overflow:hidden to direct children of body or player containers */
        body > div, #player-container > div,
        [class*="player"] > div, [id*="player"] > div,
        [class*="container"] > div {
          overflow: hidden !important;
        }

        /* Fix for absolute positioned elements */
        [style*="position: absolute"], [style*="position:absolute"] {
          max-width: 100% !important;
          max-height: 100% !important;
        }

        /* Ensure video content is centered */
        video {
          object-fit: contain !important;
        }

        /* Fix for vertical shrinking in HitsTV player */
        .plyr--video, .plyr__video-wrapper {
          height: auto !important;
          min-height: unset !important;
        }

        /* Make sure the actual video doesn't get squished */
        video, .plyr__video-embed iframe {
          position: relative !important;
          aspect-ratio: 16/9 !important;
          object-fit: contain !important;
          height: auto !important;
          max-height: 100% !important;
        }
      </style>
    `)

    // Add script to dynamically fix any scrollbar issues and player height problems
    .replace(/<body/i, `<body
      onload="(function() {
        // Force overflow hidden on body and html
        document.documentElement.style.overflow = 'hidden';
        document.body.style.overflow = 'hidden';

        // Fix HitsTV player height specifically
        function fixHitsTVPlayer() {
          // Look for HitsTV player containers
          const hitsTVContainers = document.querySelectorAll('[id*=\"hitstv\"], [class*=\"hitstv\"], .plyr--video, .video-js');

          hitsTVContainers.forEach(function(container) {
            if (container instanceof HTMLElement) {
              // Set position to relative for proper sizing
              container.style.position = 'relative';

              // Set height to auto with proper aspect ratio
              container.style.height = 'auto';
              container.style.aspectRatio = '16/9';

              // Find video elements inside the container
              const videos = container.querySelectorAll('video');
              videos.forEach(function(video) {
                video.style.position = 'relative';
                video.style.height = 'auto';
                video.style.width = '100%';
                video.style.aspectRatio = '16/9';
                video.style.objectFit = 'contain';
              });

              // Find iframe elements inside the container
              const iframes = container.querySelectorAll('iframe');
              iframes.forEach(function(iframe) {
                iframe.style.position = 'relative';
                iframe.style.height = 'auto';
                iframe.style.width = '100%';
                iframe.style.aspectRatio = '16/9';
              });
            }
          });
        }

        // Fix any player containers that get added later
        const fixScrollbars = function() {
          // Find any scrollable elements and hide their scrollbars
          const all = document.querySelectorAll('*');
          for (let i = 0; i < all.length; i++) {
            const el = all[i];
            const computedStyle = window.getComputedStyle(el);
            if (computedStyle.overflow === 'auto' ||
                computedStyle.overflow === 'scroll' ||
                computedStyle.overflowX === 'auto' ||
                computedStyle.overflowX === 'scroll' ||
                computedStyle.overflowY === 'auto' ||
                computedStyle.overflowY === 'scroll') {

              el.style.overflow = 'hidden';
              el.style.overflowX = 'hidden';
              el.style.overflowY = 'hidden';
            }

            // Fix width/height on player elements
            if (el.id &&
                (el.id.includes('player') ||
                 el.id.includes('video') ||
                 el.id.includes('container'))) {
              el.style.width = '100%';

              // Don't force absolute height on HitsTV containers
              if (el.id.includes('hitstv')) {
                el.style.height = 'auto';
                el.style.aspectRatio = '16/9';
              } else {
                el.style.height = '100%';
              }

              el.style.maxWidth = '100%';
              el.style.maxHeight = '100%';
              el.style.overflow = 'hidden';
            }

            // Same for class-based selectors
            if (el.className &&
                (el.className.includes('player') ||
                 el.className.includes('video') ||
                 el.className.includes('container'))) {
              el.style.width = '100%';

              // Don't force absolute height on HitsTV containers
              if (el.className.includes('hitstv')) {
                el.style.height = 'auto';
                el.style.aspectRatio = '16/9';
              } else {
                el.style.height = '100%';
              }

              el.style.maxWidth = '100%';
              el.style.maxHeight = '100%';
              el.style.overflow = 'hidden';
            }
          }

          // Run specialized HitsTV fixes
          fixHitsTVPlayer();
        };

        // Run immediately and then on a timer
        fixScrollbars();
        setInterval(fixScrollbars, 1000);
      })();"
    `)

    // Only remove specific problematic scripts, not all scripts
    // Remove scripts that contain frame-busting code
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*(?:top\.location|parent\.location|window\.top|self\.parent|frameElement)(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')

    // Remove scripts that contain anti-iframe code
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*(?:if\s*\(\s*window\s*!=\s*top|if\s*\(\s*self\s*!=\s*top|if\s*\(\s*parent\s*!=\s*self)(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')

    // Fix relative URLs in script src attributes to use the VidSrc domain
    .replace(/(<script[^>]+src=["'])\/([^"']+)(["'][^>]*>)/gi, `$1https://${domain}/$2$3`)

    // Fix relative URLs in link href attributes (for CSS)
    .replace(/(<link[^>]+href=["'])\/([^"']+)(["'][^>]*>)/gi, `$1https://${domain}/$2$3`)

    // Fix any remaining relative URLs in the content that might be dynamically loaded
    .replace(/src\s*=\s*["']\/([^"']+)["']/gi, `src="https://${domain}/$1"`)
    .replace(/href\s*=\s*["']\/([^"']+)["']/gi, `href="https://${domain}/$1"`)

    // Fix JavaScript code that might construct relative URLs
    .replace(/["']\/([^"']*\.(js|css|json|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot))(\?[^"']*)?["']/gi, `"https://${domain}/$1$3"`)

    // Fix any fetch() or XMLHttpRequest calls with relative URLs
    .replace(/fetch\s*\(\s*["']\/([^"']+)["']/gi, `fetch("https://${domain}/$1"`)
    .replace(/\.open\s*\(\s*["'][^"']*["']\s*,\s*["']\/([^"']+)["']/gi, `.open("GET", "https://${domain}/$1"`)

    // Replace any top.location checks
    .replace(/top\.location/g, 'window.location')

    // Replace any parent.location checks
    .replace(/parent\.location/g, 'window.location')

    // Fix style issues that might affect sizing - ensure priority with !important
    .replace(/<body/i, '<body style="margin:0 !important;padding:0 !important;height:100vh !important;width:100vw !important;overflow:hidden !important"')

    // Replace tiny/collapsed video players with full size
    .replace(/(width|height)=(["'])(\d+)(px)?(["'])/gi, '$1=$2100%$5')
    .replace(/style=(["'])([^"']*?)(width|height)\s*:\s*\d+px/gi, 'style=$1$2$3: 100%')

    // Add a wrapper to maintain aspect ratio if not already present
    .includes('<div id="player-container"') ? content :
      content.replace(/<body([^>]*)>/i, '<body$1><div id="player-container" style="position:relative !important;width:100% !important;height:100% !important;aspect-ratio:16/9 !important;overflow:hidden !important;">')
        .replace('</body>', '</div></body>');
}